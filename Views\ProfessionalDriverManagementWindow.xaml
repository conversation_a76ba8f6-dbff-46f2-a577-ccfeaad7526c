<Window x:Class="DriverManagementSystem.Views.ProfessionalDriverManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:system="clr-namespace:System;assembly=mscorlib"
        xmlns:local="clr-namespace:DriverManagementSystem"
        Title="ادارة اختيار السائقين للزيارة الميدانية"
        Height="900" Width="1600"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F7FA"
        WindowState="Maximized">

    <Window.Resources>
        <!-- Null to Boolean Converter -->
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="15" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Button Style -->
        <Style x:Key="ProfessionalButton" TargetType="Button">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="10"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5A6268"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#495057"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Primary Button -->
        <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#007BFF"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#0056B3"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button -->
        <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#FFC107"/>
            <Setter Property="Foreground" Value="#212529"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0A800"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Danger Button -->
        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#DC3545"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="White" Height="80" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid Margin="30,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#667eea" CornerRadius="50" Width="50" Height="50" Margin="0,0,15,0">
                        <TextBlock Text="🚗" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock FontSize="22" FontWeight="Bold" Foreground="#2C3E50"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="اختيار السائقين للزيارة"/></TextBlock>
                        <TextBlock FontSize="12" Foreground="#7F8C8D"><Run FlowDirection="RightToLeft" Language="ar-ye" Text="نظام اختيار السائقين للنزول الميداني"/></TextBlock>
                    </StackPanel>
                </StackPanel>

                <!-- Search Box -->
                <Border Grid.Column="1" Background="#F8F9FA" CornerRadius="25" Height="40" Margin="50,0"
                       BorderBrush="#DEE2E6" BorderThickness="1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="🔍" FontSize="16" VerticalAlignment="Center"
                                  Margin="15,0,10,0" Foreground="#6C757D"/>
                        <TextBox Grid.Column="1" x:Name="SearchTextBox"
                                Background="Transparent" BorderThickness="0"
                                FontSize="14" VerticalAlignment="Center"
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                Tag="البحث في السائقين (الاسم، الكود، الهاتف)"/>
                        <Button Grid.Column="2" Background="Transparent" BorderThickness="0"
                               Width="30" Height="30" Margin="5,0" Cursor="Hand"
                               ToolTip="مسح البحث" Click="ClearSearch_Click">
                            <TextBlock Text="✖" FontSize="12" Foreground="#6C757D"/>
                        </Button>
                    </Grid>
                </Border>

                <!-- Close Button -->
                <Button Grid.Column="2" x:Name="CloseButton" Click="CloseButton_Click"
                       Background="#DC3545" Foreground="White"
                       Width="40" Height="40"
                       BorderThickness="0" Cursor="Hand"
                       ToolTip="إغلاق">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                   CornerRadius="20"
                                   BorderThickness="{TemplateBinding BorderThickness}"
                                   BorderBrush="{TemplateBinding BorderBrush}">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Button.Template>
                    <TextBlock Text="✖" FontSize="16" FontWeight="Bold"/>
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Top Panel - Vehicle Filters and Quick Actions -->
            <Grid Grid.Row="0" Margin="0,0,0,3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="15"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Vehicle Type Filters -->
                <Border Grid.Column="0" Style="{StaticResource ModernCard}" Padding="8">
                    <StackPanel>
                        <TextBlock Text="🚗 فلترة حسب نوع المركبة وقدرتها" FontSize="12" FontWeight="Bold"
                                  Foreground="#2C3E50" Margin="0,0,0,8" HorizontalAlignment="Center"/>

                        <!-- Vehicle Types Row - Compact Design -->
                        <UniformGrid Rows="1" Columns="5" Margin="0,0,0,5">
                            <!-- Forshanal -->
                            <Border Background="#F8F9FA" CornerRadius="5" Margin="2" Padding="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <CheckBox x:Name="ForshanalFilter" Tag="فورشنال" IsChecked="{Binding IsForshanalSelected, Mode=TwoWay}"
                                             Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                             HorizontalAlignment="Center" Margin="0,0,0,3">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚐" FontSize="12" Margin="0,0,3,0"/>
                                            <TextBlock Text="فورشنال" FontWeight="Bold" FontSize="10"/>
                                        </StackPanel>
                                    </CheckBox>
                                    <TextBlock Text="(15-20)" FontSize="8" Foreground="#666" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <CheckBox x:Name="Forshanal4Cylinder" Tag="فورشنال-4بسطون" Content="4" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                        <CheckBox x:Name="Forshanal6Cylinder" Tag="فورشنال-6بسطون" Content="6" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Kanter -->
                            <Border Background="#F8F9FA" CornerRadius="5" Margin="2" Padding="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <CheckBox x:Name="KanterFilter" Tag="كنتر" IsChecked="{Binding IsKanterSelected, Mode=TwoWay}"
                                             Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                             HorizontalAlignment="Center" Margin="0,0,0,3">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚚" FontSize="12" Margin="0,0,3,0"/>
                                            <TextBlock Text="كنتر" FontWeight="Bold" FontSize="10"/>
                                        </StackPanel>
                                    </CheckBox>
                                    <TextBlock Text="(3-5 طن)" FontSize="8" Foreground="#666" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <CheckBox x:Name="Kanter4Cylinder" Tag="كنتر-4بسطون" Content="4" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                        <CheckBox x:Name="Kanter6Cylinder" Tag="كنتر-6بسطون" Content="6" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Hilux -->
                            <Border Background="#F8F9FA" CornerRadius="5" Margin="2" Padding="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <CheckBox x:Name="HiluxFilter" Tag="هيلوكس" IsChecked="{Binding IsHiluxSelected, Mode=TwoWay}"
                                             Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                             HorizontalAlignment="Center" Margin="0,0,0,3">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚙" FontSize="12" Margin="0,0,3,0"/>
                                            <TextBlock Text="هيلوكس" FontWeight="Bold" FontSize="10"/>
                                        </StackPanel>
                                    </CheckBox>
                                    <TextBlock Text="(5-7)" FontSize="8" Foreground="#666" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <CheckBox x:Name="Hilux4Cylinder" Tag="هيلوكس-4بسطون" Content="4" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                        <CheckBox x:Name="Hilux6Cylinder" Tag="هيلوكس-6بسطون" Content="6" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Bus -->
                            <Border Background="#F8F9FA" CornerRadius="5" Margin="2" Padding="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <CheckBox x:Name="BusFilter" Tag="حافلة" IsChecked="{Binding IsBusSelected, Mode=TwoWay}"
                                             Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                             HorizontalAlignment="Center" Margin="0,0,0,3">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚌" FontSize="12" Margin="0,0,3,0"/>
                                            <TextBlock Text="حافلة" FontWeight="Bold" FontSize="10"/>
                                        </StackPanel>
                                    </CheckBox>
                                    <TextBlock Text="(30-50)" FontSize="8" Foreground="#666" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <CheckBox x:Name="Bus4Cylinder" Tag="حافلة-4بسطون" Content="4" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                        <CheckBox x:Name="Bus6Cylinder" Tag="حافلة-6بسطون" Content="6" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>

                            <!-- Prado -->
                            <Border Background="#F8F9FA" CornerRadius="5" Margin="2" Padding="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <CheckBox x:Name="PradoFilter" Tag="برادو" IsChecked="{Binding IsPradoSelected, Mode=TwoWay}"
                                             Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                             HorizontalAlignment="Center" Margin="0,0,0,3">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚗" FontSize="12" Margin="0,0,3,0"/>
                                            <TextBlock Text="برادو" FontWeight="Bold" FontSize="10"/>
                                        </StackPanel>
                                    </CheckBox>
                                    <TextBlock Text="(7-8)" FontSize="8" Foreground="#666" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <CheckBox x:Name="Prado4Cylinder" Tag="برادو-4بسطون" Content="4" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                        <CheckBox x:Name="Prado6Cylinder" Tag="برادو-6بسطون" Content="6" FontSize="8" Margin="2,0"
                                                 Checked="VehicleCapacityFilter_Changed" Unchecked="VehicleCapacityFilter_Changed"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <Border Grid.Column="2" Style="{StaticResource ModernCard}" Padding="8">
                    <StackPanel>
                        <TextBlock Text="⚡ إجراءات سريعة" FontSize="12" FontWeight="Bold"
                                  Foreground="#2C3E50" Margin="0,0,0,8" HorizontalAlignment="Center"/>

                        <!-- Buttons in horizontal row -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Style="{StaticResource SuccessButton}" Height="28" Width="80" Margin="2"
                                   Click="SelectAllDrivers_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="✅" FontSize="9" Margin="0,0,2,0"/>
                                    <TextBlock Text="تحديد الكل" FontSize="9" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource ProfessionalButton}" Height="28" Width="80" Margin="2"
                                   Click="ClearSelection_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="❌" FontSize="9" Margin="0,0,2,0"/>
                                    <TextBlock Text="إلغاء التحديد" FontSize="9" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource PrimaryButton}" Height="28" Width="80" Margin="2"
                                   Click="RefreshData_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🔄" FontSize="9" Margin="0,0,2,0"/>
                                    <TextBlock Text="تحديث البيانات" FontSize="9" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Main Panel - Drivers DataGrid (Priority) -->
            <Border Grid.Row="1" Style="{StaticResource ModernCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- DataGrid Header -->
                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8,8,0,0" Padding="8,4" Margin="0,0,0,2">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="📋 قائمة السائقين" FontSize="12" FontWeight="Bold" Foreground="#2C3E50"/>
                            <TextBlock Grid.Column="1" Text="{Binding FilteredDrivers.Count, FallbackValue='0'}"
                                      FontSize="11" FontWeight="Bold" Foreground="#007BFF"/>
                        </Grid>
                    </Border>

                    <!-- Drivers DataGrid -->
                    <DataGrid Grid.Row="1" x:Name="DriversDataGrid"
                             ItemsSource="{Binding FilteredDrivers}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             CanUserReorderColumns="True"
                             SelectionChanged="DriversDataGrid_SelectionChanged"
                             CanUserResizeColumns="True"
                             CanUserSortColumns="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             SelectionMode="Extended"
                             AlternatingRowBackground="#F8F9FA"
                             RowBackground="White"
                             FontSize="11"
                             RowHeight="35">

                        <DataGrid.Columns>
                            <!-- Selection Column -->
                            <DataGridCheckBoxColumn Header="اختيار" Binding="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}" Width="60"/>

                            <!-- Driver Code -->
                            <DataGridTextColumn Header="كود السائق" Binding="{Binding DriverCode}" Width="100" IsReadOnly="True"/>

                            <!-- Driver Name -->
                            <DataGridTextColumn Header="اسم السائق" Binding="{Binding Name}" Width="200" IsReadOnly="True"/>

                            <!-- Phone Number -->
                            <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="140" IsReadOnly="True"/>

                            <!-- Vehicle Type -->
                            <DataGridTextColumn Header="نوع المركبة" Binding="{Binding VehicleType}" Width="120" IsReadOnly="True"/>

                            <!-- Vehicle Capacity -->
                            <DataGridTextColumn Header="قدرة السيارة" Binding="{Binding VehicleCapacity}" Width="120" IsReadOnly="True"/>

                            <!-- Vehicle Number -->
                            <DataGridTextColumn Header="رقم المركبة" Binding="{Binding VehicleNumber}" Width="120" IsReadOnly="True"/>

                            <!-- Contract Count -->
                            <DataGridTextColumn Header="عدد العقود" Binding="{Binding ContractCount}" Width="100" IsReadOnly="True"/>

                            <!-- Total Amount -->
                            <DataGridTextColumn Header="إجمالي المبالغ" Binding="{Binding TotalAmount}" Width="120" IsReadOnly="True"/>

                            <!-- Current Mission -->
                            <DataGridTextColumn Header="المهمة" Binding="{Binding CurrentMission}" Width="200" IsReadOnly="True"/>
                        </DataGrid.Columns>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <!-- تلوين الصف بالأحمر الخفيف إذا كان السائق في مهمة -->
                                    <DataTrigger Binding="{Binding IsInMission}" Value="True">
                                        <Setter Property="Background" Value="#FFEBEE"/>
                                    </DataTrigger>

                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                    <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                        <Setter Property="Background" Value="#BBDEFB"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>

                    <!-- DataGrid Footer -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0,0,10,10" Padding="12,6" Margin="0,5,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{Binding SelectedDrivers.Count, StringFormat='تم اختيار {0} سائق'}"
                                      FontSize="10" FontWeight="SemiBold" Foreground="#28A745"/>
                            <TextBlock Grid.Column="1" Text="{Binding FilteredDrivers.Count, StringFormat='من أصل {0} سائق'}"
                                      FontSize="10" Foreground="#6C757D"/>
                        </Grid>
                    </Border>
                </Grid>
            </Border>


        </Grid>

        <!-- Footer Section -->
        <Border Grid.Row="2" Background="White" Height="70" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid Margin="30,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Information -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📊 الحالة:" FontSize="12" FontWeight="SemiBold" Foreground="#6C757D" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding StatusMessage, FallbackValue='جاهز للاستخدام'}" FontSize="12" Foreground="#28A745" Margin="0,0,20,0"/>

                    <TextBlock Text="⏰ آخر تحديث:" FontSize="12" FontWeight="SemiBold" Foreground="#6C757D" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding LastUpdateTime, FallbackValue='الآن'}" FontSize="12" Foreground="#6C757D"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="ConfirmSelectionButton" Click="ConfirmSelectionButton_Click"
                           Style="{StaticResource SuccessButton}" Height="40" Margin="10,0" Width="161">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✅" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="تأكيد الاختيار" FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="CancelButton" Click="CancelButton_Click"
                           Style="{StaticResource DangerButton}" Height="40" Margin="10,0" Width="128">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="❌" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء" FontSize="14"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>